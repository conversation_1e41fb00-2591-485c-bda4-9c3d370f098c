
#!/usr/bin/env bash

HOME_PATH=$(cd "$(dirname "$0")";pwd)

env | grep 'OPT_PATH' &>/dev/null

if [[ $? == 0 ]]; then
    HOME_PATH=$OPT_PATH
else
    HOME_PATH='/openads/opt/'
fi

APP_PATH=$HOME_PATH/bin/vtx_serial_comm
DDS_CONF_PATH=$HOME_PATH/conf/vtx_serial_comm/vtx_serial_comm_fastdds_conf.xml
CONF_PATH=$HOME_PATH/conf/vtx_serial_comm/vtx_serial_comm_conf.json
DOMAIN_ID=80

echo "${APP_PATH} -dds ${DDS_CONF_PATH} -conf ${CONF_PATH} -domain ${DOMAIN_ID}"

${APP_PATH} -dds ${DDS_CONF_PATH} -conf ${CONF_PATH} -domain ${DOMAIN_ID}
