#include "function_vtx_serial_comm.h"

#include "common/message_util.h"
#include "common/ulog.h"
#include "time/time.h"
#include "time/rate.h"
#include "common/file.h"
#include "common/module_info.h"
#include "scheduler/scheduler.h"

#include <mutex>
#include <numeric>

constexpr int MAX_RETRY_COUNT = 3;
constexpr int RETRY_INTERVAL_MS = 2000;

VtxSerialCommFunction::VtxSerialCommFunction()
    : enable_run_(false)
    , enable_send_(false)
    , enable_signal_(false)
    , serial_connected_(false)
    , send_data_(false)
    , reconnect_attempts_(0)
    , serial_fd_(-1)
    , last_sent_mode_code_(-1)
    , current_interface_(SWITCHING_INTERFACE)
    , simulation_mode(false)
    , enable_simulation_(false)
    , sim_timestamp_(0)
    , sim_counter_(0)
    , sim_signal_counter_(0)
    , active_signal_strength_(0)
{
    SetModuleName("VtxSerialComm");
}

VtxSerialCommFunction::~VtxSerialCommFunction()
{
    Stop();
    UINFO << "release VtxSerialCommFunction";
}

void VtxSerialCommFunction::SetModuleName(std::string name)
{
    openads::common::SetModuleName(name);
}

bool VtxSerialCommFunction::InitSignalDetector() 
{
    try {
        if (!SignalDetector::getInstance().init()) 
        {
            UERROR << "Failed to initialize signal detector";
            return false;
        }

        UINFO << "Signal detector initialized successfully";
        return true;
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Exception during signal detector initialization: " << e.what();
        return false;
    }
}

bool VtxSerialCommFunction::Init(int domain, std::string &dds_file, const openads::proto::application::FunctionConfig &f_config)
{
    if (f_config.name().size() > 0)
    {
        SetModuleName(f_config.name());
    }

    // ======= mock data =========
    simulation_mode = true;
    if(simulation_mode) 
    {
        UINFO << "Starting in simulation mode";
        StartSimulationThread();
    }
    // --------------------------

    // Init signal dector
    if (!InitSignalDetector()) 
    {
        UERROR << "Signal detector initialization failed, continuing without signal detection";
    }

    // Init abstract
    if (!InitAbstract(f_config))
    {
        UERROR << "Init Abstract error!";
        return false;
    }

    // Init comm
    if (!InitComm(domain, dds_file, f_config))
    {
        UERROR << "Init Comm error!";
        return false;
    }

    UINFO << f_config.name() << " init done!";
    return true;
}

// =========== send mock data ==============
void VtxSerialCommFunction::StartSimulationThread()
{
    enable_simulation_.store(true);
    simulation_thread_ = std::thread([this]()
    {
        openads::common::time::Rate rate(20.0); 
        
        auto create_sim_msg = [&]()
        {
            auto msg = std::make_shared<Vtx_vehicleStatus>();
            auto now = std::chrono::system_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                                now.time_since_epoch()).count();
            msg->set_timestamp(timestamp);
            msg->set_watervolume(10 + sim_counter_%20);      // 60-100%波动
            msg->set_stateofcharge(20 - sim_counter_%10);    // 80-90%波动
            msg->set_boxvolume(30 + (sim_counter_/2)%70);    // 30-100%阶梯变化
            msg->set_drivingtype(1);  // 0:切换中  1：智能模式   2：人工
            msg->set_taskid("T1000");
            msg->set_emergency_stop(1); 
            msg->set_vehicleerr(sim_counter_ % 100 == 0 ? 1 : 0);
            return msg;
        };

        while(enable_simulation_.load()) 
        {
            {
                std::lock_guard<std::mutex> lock(data_mutex_);
                proto_msg_ = create_sim_msg();
                
            }

            sim_counter_++; // Increment main simulation counter
            rate.Sleep();
        }
        
    });

}
// --------------------------------------

bool VtxSerialCommFunction::InitComm(int domain, std::string &dds_file, const openads::proto::application::FunctionConfig &f_config)
{
    // Init comm
    uincomm_ptr_ = std::make_shared<openads::common::transport::Transport>(domain, dds_file);

    for (auto &c_config : f_config.comm_configs())
    {
        if (c_config.type() == openads::proto::application::CommConfig::SUB)
        {
            UINFO << "CommConfig: \n" << c_config.DebugString();
            if(c_config.abstract_name() == "VtxData")
            {
                auto dds_msg_callback = [&](const std::shared_ptr<Vtx_vehicleStatus> &msg)
                {
                    OnNewVtxDataMsg(msg);
                };
                reader_vtx_data_ = uincomm_ptr_->CreateReceiver<Vtx_vehicleStatus>(c_config, dds_msg_callback);
                reader_vtx_data_->SetMaxLatency(-1);
            }

        }
        else if (c_config.type() == openads::proto::application::CommConfig::PUB)
        {
            UINFO << "CommConfig: \n" << c_config.DebugString();
            if (c_config.abstract_name() == "VtxData")
            {
                writer_vtx_diag_ = uincomm_ptr_->CreateTransmitter<DiagnosticArray>(c_config);
            }

        }
        else
        {
            UWARN << "Unsupported Comm Type";
        }
    }
    return true;
}

bool VtxSerialCommFunction::InitAbstract(const openads::proto::application::FunctionConfig &f_config)
{
    for (auto &a_config : f_config.abstract_configs())
    {
        if (a_config.abstract_name() == "vtx_serial_comm" &&
                 a_config.m_type() == openads::proto::application::AbstractConfig::USER &&
                 a_config.s_type() == openads::proto::application::AbstractConfig::NONE)
        {
            if (!Initvtx_serial_comm(a_config))
            {
                UERROR << "Init abstract vtx_serial_comm Error!";
                return false;
            }
        }

        else
        {
            UWARN << "Unsupported Abstract Type";
        }
    }    
    return true;
}

bool VtxSerialCommFunction::Initvtx_serial_comm(const openads::proto::application::AbstractConfig &a_config)
{
    int retry_count = 0;
    bool init_success = false;

    try
        {
            std::string _work_root = openads::common::WorkRoot();
            std::string _yaml_file = _work_root + '/' + std::string("conf/vtx_serial_comm/vtx_serial_comm.yaml");
            YAML::Node config = YAML::LoadFile(_yaml_file);
            device_ = config["serial"]["device"].as<std::string>();
        }
    catch(const YAML::Exception& e)
    {
        UERROR << "Failed to load config file: " << e.what();
        return false;
    }


    while (retry_count < MAX_RETRY_COUNT && !init_success) 
    {
        serial_fd_ = open(device_.c_str(), O_RDWR | O_NOCTTY | O_SYNC);

        if (serial_fd_ < 0) 
        {
            UERROR << "Failed to open serial port " << device_.c_str()
                   << ", error: " << strerror(errno);
            retry_count++;
            std::this_thread::sleep_for(std::chrono::milliseconds(RETRY_INTERVAL_MS));
            continue;
        }

        struct termios tty;
        memset(&tty, 0, sizeof(tty));
        if (tcgetattr(serial_fd_, &tty) != 0) 
        {
            UERROR << "tcgetattr failed: " << strerror(errno);
            close(serial_fd_);
            serial_fd_ = -1;
            retry_count++;
            std::this_thread::sleep_for(std::chrono::milliseconds(RETRY_INTERVAL_MS));
            continue;
        }

        cfsetospeed(&tty, B115200);
        cfsetispeed(&tty, B115200);
        tty.c_cflag &= ~CSTOPB; 
        tty.c_cflag &= ~PARENB; 
        tty.c_cflag &= ~CSIZE;
        tty.c_cflag |= CS8;     
        tty.c_cflag &= ~CRTSCTS; 
        tty.c_cc[VMIN] = 0;     
        tty.c_cc[VTIME] = 5;  
    
        if (tcsetattr(serial_fd_, TCSANOW, &tty) != 0) 
        {
            UERROR << "tcsetattr failed: " << strerror(errno);
            close(serial_fd_);
            serial_fd_ = -1;
            retry_count++;
            std::this_thread::sleep_for(std::chrono::milliseconds(RETRY_INTERVAL_MS));
            continue;
        }

        tcflush(serial_fd_, TCIOFLUSH);
        UINFO << "Serial port initialized successfully";
        init_success = true;
    }

    if (!init_success) 
    {
        UERROR << "Failed to initialize serial port after " << MAX_RETRY_COUNT << " attempts";
        return false;
    }

    serial_connected_.store(true);

    return true;
}

void VtxSerialCommFunction::Run()
{
    enable_run_.store(true);
    enable_send_.store(true);
    enable_signal_.store(true);
    
    StartSendThread();

    diagnosticArray_ = std::make_shared<DiagnosticArray>();
    threads_.emplace_back(std::bind(&VtxSerialCommFunction::DiagnosticThread, this));

    AbstractRun();
}

void VtxSerialCommFunction::StartSendThread()
{
    threads_.emplace_back([this] 
    {
        UINFO << "Starting unified send thread at 20Hz";

        openads::common::time::Rate rate(20.0);
        int tick = 0;

        const int interval_time   = 20 * 60 / TIME_FREQ;   
        const int interval_status = 20 * 60 / STATUS_FREQ; 
        const int interval_mode   = 20 / MODE_FREQ;        
        const int interval_task   = 20 / TASK_FREQ;        
        const int interval_error  = 20 / ERROR_FREQ;      

        while (enable_send_.load()) 
        {
            if (serial_connected_.load())
            {

                // TimeFrame
                if (tick % interval_time == 0) 
                {
                    auto frames = BuildTimeFrame();
                    for (const auto& f : frames) 
                    {
                        if (!f.empty() && SendFrame(f)) 
                        {
                            send_data_.store(true);
                            UINFO << "Sent TimeFrame";
                        } 
                        else 
                        {
                            UERROR << "Failed to send TimeFrame";
                        }
                    }
                }

                // StatusFrame
                if (tick % interval_status == 0) 
                {
                    auto frames = BuildStatusFrame();
                    for (const auto& f : frames) 
                    {
                        if (!f.empty() && SendFrame(f)) 
                        {
                            send_data_.store(true);
                            UINFO << "Sent StatusFrame";
                        } 
                        else 
                        {
                            UERROR << "Failed to send StatusFrame";
                        }
                    }
                }

                // ModeFrame
                if (tick % interval_mode == 0) 
                {
                    auto frames = BuildModeFrame();
                    for (const auto& f : frames) 
                    {
                        if (!f.empty() && SendFrame(f)) 
                        {
                            send_data_.store(true);
                            UINFO << "Sent ModeFrame";
                        } 
                        else 
                        {
                            UERROR << "Failed to send ModeFrame";
                        }
                    }
                }

                // TaskFrame
                if (tick % interval_task == 0) 
                {
                    auto frames = BuildTaskFrame();
                    for (const auto& f : frames) 
                    {
                        if (!f.empty() && SendFrame(f)) 
                        {
                            send_data_.store(true);
                            UINFO << "Sent TaskFrame";
                        } 
                        else 
                        {
                            UERROR << "Failed to send TaskFrame";
                        }
                    }
                }

                // ErrorFrame
                if (tick % interval_error == 0) 
                {
                    auto frames = BuildErrorFrame();
                    for (const auto& f : frames) 
                    {
                        if (!f.empty() && SendFrame(f)) 
                        {
                            send_data_.store(true);
                            UINFO << "Sent ErrorFrame";
                        } 
                        else 
                        {
                            UERROR << "Failed to send ErrorFrame";
                        }
                    }
                }
            }

            tick++;
            if (tick >= 1000) tick = 0; 
            rate.Sleep();
        }
    });
}

int VtxSerialCommFunction::GetActualResourceId(BaseFrameType base_type, InterfaceType interface_type)
{
    UINFO << "组件ID: " << 100 + static_cast<int>(interface_type) * 100 + static_cast<int>(base_type);
    return 100 + static_cast<int>(interface_type) * 100 + static_cast<int>(base_type);
}

std::string VtxSerialCommFunction::BuildUniformFrame(int resource_id, const std::string& value) 
{
    std::stringstream ss;
    ss << "@SET " << resource_id << "," << value << "\r";
    UINFO << "Frame content: " << ss.str();
    return ss.str();
}

std::vector<std::string> VtxSerialCommFunction::BuildTimeFrame() 
{
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (!proto_msg_) return {};

    UINFO << "时间： " << proto_msg_->timestamp();

    time_t rawtime = proto_msg_->timestamp() / 1000;
    struct tm timeinfo;
    localtime_r(&rawtime, &timeinfo);

    char time_buffer[15];
    strftime(time_buffer, sizeof(time_buffer), "%H/%M/%S", &timeinfo);
    std::stringstream timers_ss;
    timers_ss << "@TIMERS ," << time_buffer << "\r";

    int year = timeinfo.tm_year + 1900;
    int month = timeinfo.tm_mon + 1;
    int day = timeinfo.tm_mday;
    char date_buffer[30];
    snprintf(date_buffer, sizeof(date_buffer), "%d/%d/%d", year, month, day);
    std::stringstream datas_ss;
    datas_ss << "@DATAS ," << date_buffer << "\r";

    return {timers_ss.str(), datas_ss.str()};
}

std::vector<std::string> VtxSerialCommFunction::BuildStatusFrame() 
{
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (!proto_msg_) return {};

    int current_signal_strength = 0;
    {
        std::lock_guard<std::mutex> signal_lock(signal_mutex_);
        current_signal_strength = active_signal_strength_;
    }

    int signal_icon_value = 0;
    if (current_signal_strength > 75) 
    {
        signal_icon_value = 3;
    } 
    else if (current_signal_strength > 50) 
    {
        signal_icon_value = 2;
    } 
    else if (current_signal_strength > 25) 
    {
        signal_icon_value = 1;
    } 
    else 
    {
        signal_icon_value = 0;
    }

    std::vector<std::string> frames;

    InterfaceType interface_type = current_interface_;
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(WATER_VOLUME_ICON_BASE, interface_type), 
        std::to_string(proto_msg_->watervolume() / 10)));
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(WATER_VOLUME_VALUE_BASE, interface_type), 
        std::to_string(proto_msg_->watervolume())));
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(BATTERY_ICON_BASE, interface_type), 
        std::to_string(proto_msg_->stateofcharge() / 10)));
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(BATTERY_VALUE_BASE, interface_type), 
        std::to_string(proto_msg_->stateofcharge())));
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(BOX_VOLUME_ICON_BASE, interface_type), 
        std::to_string(proto_msg_->boxvolume() / 10)));
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(BOX_VOLUME_VALUE_BASE, interface_type), 
        std::to_string(proto_msg_->boxvolume())));
    frames.push_back(BuildUniformFrame(
        GetActualResourceId(SIGNAL_ICON_BASE, interface_type), 
        std::to_string(signal_icon_value)));

    return frames;
}

std::vector<std::string> VtxSerialCommFunction::BuildErrorFrame() 
{
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (!proto_msg_) return {};

    std::vector<std::string> frames;

    InterfaceType interface_type = current_interface_;

    UINFO << "statusframe type" << interface_type;
    UINFO << "water:" << proto_msg_->watervolume();
    UINFO << "stateofcharge: " << proto_msg_->stateofcharge();
    UINFO << "box: " << proto_msg_->boxvolume();

    if (proto_msg_->emergency_stop() == 1) 
    {  
        int error_code = proto_msg_->vehicleerr();
        frames.push_back(BuildUniformFrame(
            GetActualResourceId(ESTOP_BASE, interface_type), "1"));
        frames.push_back(BuildUniformFrame(
            GetActualResourceId(WARNING_ICON_BASE, interface_type), "1"));
        frames.push_back(BuildUniformFrame(
            GetActualResourceId(WARNING_VALUE_BASE, interface_type), 
            std::to_string(error_code)));
    } 
    else 
    {  
        int error_code = proto_msg_->vehicleerr();
        if (error_code != 0) 
        {  
            frames.push_back(BuildUniformFrame(
                GetActualResourceId(ESTOP_BASE, interface_type), "0"));
            frames.push_back(BuildUniformFrame(
                GetActualResourceId(WARNING_ICON_BASE, interface_type), "1"));
            frames.push_back(BuildUniformFrame(
                GetActualResourceId(WARNING_VALUE_BASE, interface_type), 
                std::to_string(error_code)));
        } 
        else 
        {  
            frames.push_back(BuildUniformFrame(
                GetActualResourceId(ESTOP_BASE, interface_type), "0"));
            frames.push_back(BuildUniformFrame(
                GetActualResourceId(WARNING_ICON_BASE, interface_type), "0"));
            frames.push_back(BuildUniformFrame(
                GetActualResourceId(WARNING_VALUE_BASE, interface_type), "0"));
        }
    }

    return frames;
}

std::vector<std::string> VtxSerialCommFunction::BuildModeFrame()
{
    std::lock_guard<std::mutex> lock(data_mutex_);
    // if (!proto_msg_) return {};

    if (proto_msg_)
    {
        proto_msg_->set_drivingtype(2);
    }

    int current_mode_code = 0; 
    InterfaceType target_interface = SWITCHING_INTERFACE;
    
    switch (proto_msg_->drivingtype())
    {
        case 0: 
            current_mode_code = 0; 
            target_interface = SWITCHING_INTERFACE;
            break;
        case 1: 
            current_mode_code = 1; 
            target_interface = SMART_INTERFACE;
            break;
        default: 
            current_mode_code = 2; 
            target_interface = MANUAL_INTERFACE;
            break;
    }

    if (current_mode_code != last_sent_mode_code_) 
    {
        last_sent_mode_code_ = current_mode_code; 
        current_interface_ = target_interface;
        std::stringstream ss;
        ss << "@GUIS " << current_mode_code << "\r";
        UINFO << "Mode Changed. Sending Mode Frame: " << ss.str();
        return {ss.str()}; 
    }

    return {}; 
}

std::vector<std::string> VtxSerialCommFunction::BuildTaskFrame() 
{
    std::lock_guard<std::mutex> lock(data_mutex_);
    if (!proto_msg_ || proto_msg_->drivingtype() != 1)
    {
        return {};
    }

    int task_status = 0;  // drivingtype==1 时作业中
    return 
    {
        BuildUniformFrame(GetActualResourceId(TASK_ID_BASE, SMART_INTERFACE), proto_msg_->taskid()),
        BuildUniformFrame(GetActualResourceId(MODE_TEXT_BASE, SMART_INTERFACE), std::to_string(task_status))
    };
}




bool VtxSerialCommFunction::SendFrame(const std::string &frame)
{
    if(serial_fd_ == -1 || !serial_connected_.load()) 
    {
        UWARN << "Serial not connected, dropping frame";
        return false;
    }

    int retry_count = 0;
    const int max_retries = 3;
    
    while (retry_count < max_retries) 
    {
        ssize_t written = write(serial_fd_, frame.c_str(), frame.size());
        if(written == static_cast<ssize_t>(frame.size())) 
        {
            // UINFO << "Successfully sent frame: " << frame.substr(0, frame.size()-1);
            return true;
        }
        
        UERROR << "Failed to write full frame (attempt " << retry_count + 1 
               << "): " << written << "/" << frame.size() << " bytes written";
        
        retry_count++;
        if (retry_count < max_retries) 
        {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    UERROR << "Failed to send frame after " << max_retries << " attempts";
    return false;
}


void VtxSerialCommFunction::Stop()
{
    static std::atomic<bool> stop_called{false};
    if (stop_called.exchange(true)) 
    {
        UINFO << "Stop() already called, skipping";
        return;
    }

    UINFO << "Starting VtxSerialCommFunction shutdown...";

    enable_run_.store(false);
    enable_send_.store(false);
    enable_signal_.store(false);
    enable_simulation_.store(false);

    try 
    {
        if (reader_vtx_data_) 
        {
            reader_vtx_data_->Disable();
        }
        if (uincomm_ptr_) 
        {
            uincomm_ptr_->Shutdown();
        }
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error during communication shutdown: " << e.what();
    }

    try 
    {
        if (simulation_thread_.joinable()) 
        {
            simulation_thread_.join();
            UINFO << "Simulation thread joined";
        }
        if (signal_thread_.joinable())
         {
            signal_thread_.join();
            UINFO << "Signal thread joined";
        }
        for (auto &t : threads_) 
        {
            if (t.joinable()) 
            {
                t.join();
            }
        }
        threads_.clear();
        UINFO << "All worker threads joined";
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error joining threads: " << e.what();
    }

    {
        std::lock_guard<std::mutex> lock(data_mutex_);
        proto_msg_.reset();
    }

    if (serial_fd_ != -1) 
    {
        close(serial_fd_);
        serial_fd_ = -1;
        serial_connected_.store(false);
        UINFO << "Serial port closed";
    }


    try 
    {
        AbstractStop();
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error during AbstractStop: " << e.what();
    }

    UINFO << "VtxSerialCommFunction stopped and resources released.";
}


void VtxSerialCommFunction::CommStop()
{

}

void VtxSerialCommFunction::AbstractRun()
{
    ret_VtxSerialCommvtx_serial_comm_ = openads::common::scheduler::Scheduler::Instance()->Async("VtxSerialCommvtx_serial_commRun", &VtxSerialCommFunction::VtxSerialCommvtx_serial_commRun, this);
    
    openads::common::scheduler::Scheduler::Instance()->NotifyOne("VtxSerialCommvtx_serial_commRun");

    return;
}

void VtxSerialCommFunction::AbstractStop()
{
    try {
        if (ret_VtxSerialCommvtx_serial_comm_.valid()) {
            ret_VtxSerialCommvtx_serial_comm_.get();
            UINFO << "Abstract task VtxSerialCommvtx_serial_comm stopped";
        }
    } catch (const std::exception& e) {
        UERROR << "Error stopping abstract task: " << e.what();
    }
}

void VtxSerialCommFunction::OnNewVtxDataMsg(const std::shared_ptr<Vtx_vehicleStatus> &proto_msg)
{
    if (!enable_run_.load())
    {
        return;
    }

    try
    {
        UINFO << "Reader VtxData callback ";
        std::lock_guard<std::mutex> lock(data_mutex_);
        proto_msg_ = proto_msg;
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error in VtxData callback: " << e.what();
    }
}

void VtxSerialCommFunction::VtxSerialCommvtx_serial_commRun()
{
    UINFO << "Abstract VtxSerialCommvtx_serial_comm Run";

    signal_thread_ = std::thread([this]() 
    {
        UINFO << "Signal detection thread started";
        openads::common::time::Rate rate(1.0); 
        
        while (enable_signal_.load()) 
        {
            int current_active_strength = 0;
            if (simulation_mode) 
            {
                current_active_strength = (sim_signal_counter_ * 7) % 101; 
                sim_signal_counter_++; 
                UINFO << "Simulating active signal strength: " << current_active_strength << "%";
            }
            else
            {
                try 
                {
                    int yr_strength = 0, xc_strength = 0;
                    std::tie(yr_strength, xc_strength) = SignalDetector::getInstance().getSignalStrengths(); 
                    
                    if (yr_strength > 0) 
                    {
                        current_active_strength = yr_strength;
                        UINFO << "Using YR signal strength: " << yr_strength << "%";
                    } 
                    else if (xc_strength > 0) 
                    {
                        current_active_strength = xc_strength;
                        UINFO << "Using XC signal strength: " << xc_strength << "%";
                    } 
                    else 
                    {
                        UINFO << "No valid signal strength detected.";
                        current_active_strength = 0;
                    }
                } 
                catch (const std::exception& e) 
                {
                    UERROR << "Error in signal detection: " << e.what();
                    current_active_strength = 0; // Default to 0 on error
                }
            }

            {
                std::lock_guard<std::mutex> lock(signal_mutex_);
                active_signal_strength_ = current_active_strength; 
            }

            rate.Sleep();
        }
        UINFO << "Signal processing thread stopped";
    });
}

void VtxSerialCommFunction::Diagnostic()
{
    if (!serial_connected_)
    {
        SetDiagnosticStatus("serial_connected failed", openads::proto::monitor::DiagnosticStatus::WARN, diag::DRIVER_ERROR_VTX_SERIAL_COMM);
    }
    else if (!send_data_)
    {
        SetDiagnosticStatus("send data failed", openads::proto::monitor::DiagnosticStatus::WARN, diag::DRIVER_WARN_VTX_SERIAL_COMM);
    }
    
    else
    {
        SetDiagnosticStatus("Vtx serial communication working normally", openads::proto::monitor::DiagnosticStatus::OK, diag::DRIVER_OK_VTX_SERIAL_COMM);
    }
    
}

void VtxSerialCommFunction::SetDiagnosticStatus(const std::string& message, const openads::proto::monitor::DiagnosticStatus::Level level, const uint32_t error_code)
{
    openads::proto::monitor::DiagnosticStatus* status = nullptr;
    if (diagnosticArray_->status_size() > 0) {
        status = diagnosticArray_->mutable_status(0);
    } else {
        status = diagnosticArray_->add_status();
    }
    status->set_name("vtxdiag");
    status->set_message(message);
    status->set_level(level);
    UINFO << "DiagnosticStatus: " << status->DebugString();
}

void VtxSerialCommFunction::DiagnosticThread()
{
    constexpr double kDiagnosticFreq = 1.0; 
    openads::common::time::Rate rate(kDiagnosticFreq);

    while (enable_run_.load()) 
    {
        // rate.Reset();
        openads::common::FillHeader("vtxdiag", diagnosticArray_.get(), "diagnostic_vtxdiag");

        Diagnostic();
        if (writer_vtx_diag_) 
        {
            writer_vtx_diag_->Write(diagnosticArray_);
        }
        if (!enable_run_.load()) 
        break;

        rate.Sleep();
    }
}