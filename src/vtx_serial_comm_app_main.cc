#define BOOST_BIND_GLOBAL_PLACEHOLDERS
#include <boost/property_tree/ptree.hpp>
#include <boost/property_tree/json_parser.hpp>
#include <boost/log/core.hpp>
#include <boost/log/trivial.hpp>
#include <boost/log/expressions.hpp>
#include <csignal>
#include <sched.h>

#include "common/file.h"
#include "openads/proto/application/application_config.pb.h"
#include "scheduler/scheduler.h"
#include "function_vtx_serial_comm.h"

namespace
{
    std::function<void(int)> shutdown_handler;
    void signal_handler(int signal) { shutdown_handler(signal); }
} // namespace

// APP: vtx_serial_comm
int main(int argc, char **argv)
{
    google::InitGoogleLogging(argv[0]);
    google::SetLogDestination(google::ERROR, "");
    google::SetLogDestination(google::WARNING, "");
    google::SetLogDestination(google::FATAL, "");
    google::SetLogFilenameExtension(".log");
    google::SetStderrLogging(google::GLOG_INFO);
    
    bool paramsOk = false;
    int input_domain_id = 0;
    std::string config_file = "";

    if (argc >= 2)
    {
        if (argc % 2 != 1)
        {
            std::cout << "Error: arguments not paired!"
                      << " argc:" << argc << std::endl;
            paramsOk = false;
        }
        else
        {
            for (int i = 1; i < argc; i += 2)
            {
                if (strcmp(argv[i], "-conf") == 0)
                {
                    config_file = argv[i + 1];
                    std::cout << argv[i] << " : " << argv[i + 1] << std::endl;
                    paramsOk = true;
                }
                else if (strcmp(argv[i], "-domain") == 0)
                {
                    input_domain_id = atoi(argv[i + 1]);
                    std::cout << argv[i] << " : " << argv[i + 1] << std::endl;
                }
                else
                {
                    // paramsOk = false;
                    // paramsOk = true;
                    std::cout << "Error: wrong arguments! " << argv[i] << std::endl;
                }
            }
        }
    }
    else
    {
        std::cout << "Error: need arguments input!"
                  << " argc:" << argc << std::endl;
        paramsOk = false;
    }

    if (!paramsOk)
    {
        std::cout << "-------------------------------------------------------------------- " << std::endl;
        std::cout << "Arguments needed: [-conf] " << std::endl;
        std::cout << "	-conf: .json file of user configures " << std::endl;
        std::cout << "Arguments optional: [-domain] " << std::endl;
        std::cout << "	-domain: Domain id; Default:80 " << std::endl;
        std::cout << "-------------------------------------------------------------------- " << std::endl;
        return 0;
    }

    boost::property_tree::ptree module_config;
    boost::optional<boost::property_tree::ptree> log_config;
    try
    {
        boost::property_tree::ptree config;
        boost::property_tree::read_json(config_file, config);
        module_config = config.get_child("vtx_serial_comm");
        log_config = config.get_child_optional("log");
    }
    catch (const std::runtime_error &e)
    {
        UERROR << "Parse and retrieve config from JSON file " << config_file << " failed:" << e.what();
        return 0;
    }

    signal(SIGINT, SIG_IGN);
    signal(SIGINT, signal_handler);

    UINFO << "Start initiating: ";
    
    std::string work_root = openads::common::WorkRoot();

    std::string config_file_path = module_config.get<std::string>("config_file");
    std::string flag_file_path = module_config.get<std::string>("gflag_file");

    std::string app_config = work_root + "/" + config_file_path;
    openads::proto::application::AppConfig config;
    UINFO << "AppConfig path: " << app_config;
    if (!openads::common::GetProtoFromFile(app_config, &config))
    {
        UERROR  << "load AppConfig failed";
        return 0;
    }
    UINFO << "AppConfig: \n" << config.DebugString();;

    if(config.function_configs().size() <= 0){
        UERROR  << "Empty function config!";
        return 0;
    }

    int domain_id = config.domain_id();
    if(input_domain_id != 0){
        domain_id = input_domain_id;
        UWARN  << "Using input domain id: " << domain_id;
    }
    std::string dds_file = config.qos_path();

    int conf_num = openads::common::scheduler::Scheduler::Instance()->AddAppThreadConf(config);
    
    UINFO << "Creat " << conf_num  << " Task";

    /* User ******/
    std::shared_ptr<VtxSerialCommFunction> vtx_serial_comm_ptr;
    vtx_serial_comm_ptr = std::make_shared<VtxSerialCommFunction>();
    bool is_vtx_serial_comm_init = false;
 
    for(auto f_config : config.function_configs())
    {
        if(f_config.name() == "VtxSerialCommFunc" && f_config.enable_app())
        {
            is_vtx_serial_comm_init = vtx_serial_comm_ptr->Init(domain_id, dds_file, f_config);
            if(is_vtx_serial_comm_init)
            {
                vtx_serial_comm_ptr->Run();
            }
        }

        else
        {
            UWARN << "Unsupported Function Name: " << f_config.name();
        }
    }
    /****** User */

    bool run = true;
    std::mutex main_mutex;
    std::condition_variable main_condition;
    bool exit_main_flag = false;

    shutdown_handler = [&](int sig)
    {
        UINFO << "Handle SIGINT from ctrl-c";

        /* User ******/
        try {
            if(is_vtx_serial_comm_init && vtx_serial_comm_ptr)
            {
                UINFO << "Stopping VtxSerialCommFunction...";
                vtx_serial_comm_ptr->Stop();
                UINFO << "VtxSerialCommFunction stopped successfully";
            }
        } catch (const std::exception& e) {
            UERROR << "Error during shutdown: " << e.what();
        } catch (...) {
            UERROR << "Unknown error during shutdown";
        }

        /****** User */

        sleep(1);
        // run = false;
        {
            std::unique_lock<std::mutex> lock(main_mutex);
            exit_main_flag = true;
        }
        main_condition.notify_one();

        UINFO << "After interrupt()";
    };

    while (run)
    {
        {
            std::unique_lock<std::mutex> lock(main_mutex);
            main_condition.wait(lock, [&]()
                                    { return exit_main_flag; });
        }
        UINFO << "exit main!";
        sleep(1);
        run = false;
    }

    UINFO << "Cleaning up resources...";

    // Explicit cleanup to ensure proper resource release
    try {
        if (is_vtx_serial_comm_init && vtx_serial_comm_ptr) {
            vtx_serial_comm_ptr->Stop();
            vtx_serial_comm_ptr.reset();
            UINFO << "VtxSerialCommFunction cleaned up";
        }
    } catch (const std::exception& e) {
        UERROR << "Error during final cleanup: " << e.what();
    }

    UINFO << "Application shutdown complete";

    return 0;
}
