#pragma once

#include "common/environment.h"
#include "openads/proto/application/application_config.pb.h"
#include "openads/proto/monitor/system_status.pb.h" 
#include "openads/proto/vtx/vtx_cloud.pb.h"
#include "signal_detector.h"
#include "transport/c_transport_api.h"
#include "XC_Detector.h"
#include "YR_Detector.h"

#include <atomic>
#include <condition_variable>
#include <future>
#include <mutex>
#include <termios.h>
#include <yaml-cpp/yaml.h>

using vtx::protobuf::cloud::transform::Vtx_vehicleStatus;
using openads::proto::monitor::DiagnosticArray;

namespace diag
{
  const std::uint32_t DRIVER_ERROR_VTX_SERIAL_COMM = 17300;
  const std::uint32_t DRIVER_WARN_VTX_SERIAL_COMM = 17200;
  const std::uint32_t DRIVER_OK_VTX_SERIAL_COMM = 17000;

  const std::map<uint32_t, std::string> error_infos = {
    {DRIVER_ERROR_VTX_SERIAL_COMM, "VTX Serial Communication Error"},
    {DRIVER_WARN_VTX_SERIAL_COMM, "VTX Serial Communication Warning"},
    {DRIVER_OK_VTX_SERIAL_COMM, "VTX Serial Communication OK"}
  };
}


enum BaseFrameType 
{        
  WATER_VOLUME_ICON_BASE = 6,    
  WATER_VOLUME_VALUE_BASE = 7,   
  BATTERY_ICON_BASE = 8,         
  BATTERY_VALUE_BASE = 9,        
  BOX_VOLUME_ICON_BASE = 4,      
  BOX_VOLUME_VALUE_BASE = 5,     
  SIGNAL_ICON_BASE = 10,          
  WARNING_ICON_BASE = 12,         
  WARNING_VALUE_BASE = 13,        
  ESTOP_BASE = 1,               
  MODE_TEXT_BASE = 0,           // 智能模式下的作业模式提示文字(200)
  TASK_ID_BASE = 11,            // 智能模式下的任务ID(211)
  DATE_BASE = 2,                // 日期(102,202,302)
  TIME_BASE = 3                 // 时间(103,203,303)
};

// 界面类型
enum InterfaceType 
{
  SWITCHING_INTERFACE = 0,  // 模式切换中界面
  SMART_INTERFACE = 1,      // 智能模式界面
  MANUAL_INTERFACE = 2      // 人工模式界面
};

enum Frequency 
{
  TIME_FREQ = 1,      // Time frame frequency: once per minute
  STATUS_FREQ = 10,   // Status frame frequency: 10 times per minute
  MODE_FREQ = 4,       // Mode frame frequency: 4 times per second
  TASK_FREQ = 1,       // Task frame frequency: once per second
  ERROR_FREQ = 10      // Error frame frequency: 10 times per second
};

class VtxSerialCommFunction
{
public:
  VtxSerialCommFunction();
  virtual ~VtxSerialCommFunction();
  bool Init(int domain, std::string &dds_file, const openads::proto::application::FunctionConfig &f_config);
  void Run();
  void Stop();
  
private:
  void SetModuleName(std::string name);
  bool InitAbstract(const openads::proto::application::FunctionConfig &f_config);
  bool InitComm(int domain, std::string &dds_file, const openads::proto::application::FunctionConfig &f_config);
  void CommStop();
  void AbstractRun();
  void AbstractStop();
  bool Initvtx_serial_comm(const openads::proto::application::AbstractConfig &a_config);
  bool InitSignalDetector();
  void VtxSerialCommvtx_serial_commRun();
  void StartSendThread();
  bool SendFrame(const std::string &frame);
  void OnNewVtxDataMsg(const std::shared_ptr<Vtx_vehicleStatus> &proto_msg);
  void StartSimulationThread();  // mock data
  void Diagnostic();
  void DiagnosticThread(); 
  void SetDiagnosticStatus(const std::string& message, const openads::proto::monitor::DiagnosticStatus::Level level, const uint32_t error_code);
  
  // 辅助函数：根据基础ID和界面ID生成实际资源ID
  int GetActualResourceId(BaseFrameType base_type, InterfaceType interface_type);
  
  // 构建统一格式的帧
  std::string BuildUniformFrame(int resource_id, const std::string& value);
  
  // 构建各种类型的帧
  std::vector<std::string> BuildTimeFrame();
  std::vector<std::string> BuildStatusFrame();
  std::vector<std::string> BuildModeFrame();
  std::vector<std::string> BuildTaskFrame();
  std::vector<std::string> BuildErrorFrame();
  
  std::vector<std::thread> threads_;
  std::shared_ptr<openads::common::transport::Transport> uincomm_ptr_;
  std::future<void> ret_VtxSerialCommvtx_serial_comm_;
  std::shared_ptr<openads::common::transport::Receiver<Vtx_vehicleStatus>> reader_vtx_data_;
  std::shared_ptr<openads::common::transport::Transmitter<DiagnosticArray>> writer_vtx_diag_;
  std::shared_ptr<DiagnosticArray> diagnosticArray_;
  std::shared_ptr<Vtx_vehicleStatus> proto_msg_;
  std::atomic<bool> enable_run_;
  std::atomic<bool> enable_send_;
  std::atomic<bool> enable_signal_{false};
  std::atomic<int> serial_connected_{false};
  std::atomic<int> send_data_{false};
  std::atomic<int> reconnect_attempts_{0};
  std::mutex data_mutex_;
  int serial_fd_;
  int last_sent_mode_code_ = -1; 
  std::string device_;
  
  // 当前界面类型
  InterfaceType current_interface_ = SWITCHING_INTERFACE;

  // ========= mock data ===============
  bool simulation_mode = false;
  std::atomic<bool> enable_simulation_{false};
  std::thread simulation_thread_;
  uint64_t sim_timestamp_ = 0;
  int sim_counter_ = 0; 
  int sim_signal_counter_ = 0; // Counter specifically for signal simulation
  // -----------------------------------

  std::thread signal_thread_;
  std::mutex signal_mutex_;
  int active_signal_strength_{0}; // Stores strength of the detected router (YR or XC)
  
};//class VtxSerialCommFunction