
#include "XC_Detector.h"
#include <sstream>

#define SNMP_OID_GET_CPU_INFO ("*******.2.1.32.3.101.1")
#define SNMP_OID_GET_IMEI_INFO ("*******.4.1.61296.6.1.1.101.1")
#define SNMP_OID_GET_VERSION_INFO ("*******.2.1.32.5.101.1")
#define SNMP_OID_GET_DEV_ICCID_INFO ("*******.2.1.33.3.101.1")
#define SNMP_OID_GET_NET_TYPE_INFO ("*******.2.1.33.5.101.1")
#define SNMP_OID_GET_IMSI_INFO ("*******.2.1.33.4.101.1")
#define SNMP_OID_GET_NET_RSSI_INFO ("*******.4.1.61296.6.3.7.101.1")
#define SNMP_OID_GET_NET_TAC_INFO ("*******.4.1.61296.*********.1")

std::string kusaauto::XC_Detector::Result::printString() 
{
    std::stringstream ss;
    ss << "CPU Usage: " << cpu << "\n"
       << "IMEI: " << imei << "\n"
       << "Version: " << version << "\n"
       << "RSSI: " << rssi << "\n"
       << "ICCID: " << iccid << "\n"
       << "IMSI: " << imsi << "\n"
       << "Net Type: " << net_type << "\n"
       << "TAC: " << tac << "\n";
    return ss.str();
}

kusaauto::XC_Detector::XC_Detector(
    const std::string& host,
    const std::string& user,
    const std::string& password,
    bool simpleMode,
    int timeout)
    : m_host(host), m_user(user), m_password(password), m_simpleMode(simpleMode), m_timeout(timeout), m_pSnmp(nullptr) {
    Snmp_pp::DefaultLog::log()->set_filter(ERROR_LOG, 7);
    Snmp_pp::DefaultLog::log()->set_filter(WARNING_LOG, 7);
    Snmp_pp::DefaultLog::log()->set_filter(EVENT_LOG, 7);
    Snmp_pp::DefaultLog::log()->set_filter(INFO_LOG, 0);
    Snmp_pp::DefaultLog::log()->set_filter(DEBUG_LOG, 0);
}

kusaauto::XC_Detector::~XC_Detector() 
{
    if (nullptr != m_pSnmp) {
        delete m_pSnmp;
        m_pSnmp = nullptr;
    }
    if (nullptr != m_pV3MP) {
        delete m_pV3MP;
        m_pV3MP = nullptr;
    }
}

kusaauto::XC_Detector::ResultPtr kusaauto::XC_Detector::detectXCSiganl() 
{
    if (nullptr == m_pSnmp) 
    {
        if (0 != init()) 
        {
            std::cout << "Error creating SNMP session" << std::endl;
        }
    }
    Snmp_pp::UTarget target(Snmp_pp::IpAddress(m_host.c_str()));
    target.set_version(Snmp_pp::version3);
    target.set_security_model(SNMP_SECURITY_MODEL_USM);
    target.set_security_name(m_user.c_str());
    target.set_timeout(1000);

    Snmp_pp::Pdu pdu;
    pdu.set_security_level(SNMP_SECURITY_LEVEL_AUTH_NOPRIV);
    pdu += Snmp_pp::Vb(SNMP_OID_GET_CPU_INFO);
    pdu += Snmp_pp::Vb(SNMP_OID_GET_NET_RSSI_INFO);

    if (!m_simpleMode) 
    {
        pdu += Snmp_pp::Vb(SNMP_OID_GET_NET_TYPE_INFO);
        pdu += Snmp_pp::Vb(SNMP_OID_GET_IMEI_INFO);
        pdu += Snmp_pp::Vb(SNMP_OID_GET_VERSION_INFO);
        pdu += Snmp_pp::Vb(SNMP_OID_GET_DEV_ICCID_INFO);
        pdu += Snmp_pp::Vb(SNMP_OID_GET_IMSI_INFO);
        pdu += Snmp_pp::Vb(SNMP_OID_GET_NET_TAC_INFO);
    }

    int status;
    if (SNMP_CLASS_SUCCESS != (status = m_pSnmp->get(pdu, target))) 
    {
        std::cout << "Error get descriptor, " << m_pSnmp->error_msg(status) << std::endl;

        delete m_pSnmp;
        m_pSnmp = nullptr;
        delete m_pV3MP;
        m_pV3MP = nullptr;
        return nullptr;
    }

    ResultPtr result = std::make_unique<Result>();
    int cnt = pdu.get_vb_count();
    for (int i = 0; i < cnt; ++i) 
    {
        const Snmp_pp::Vb vb = pdu.get_vb(i);
        std::string oid = vb.get_printable_oid(); 
        std::string value = vb.get_printable_value(); 

        if (oid == SNMP_OID_GET_CPU_INFO) 
        {
            result->cpu = value;
        } 
        else if (oid == SNMP_OID_GET_IMEI_INFO) 
        {
            result->imei = value;
        } 
        else if (oid == SNMP_OID_GET_VERSION_INFO) 
        {
            result->version = value;
        } 
        else if (oid == SNMP_OID_GET_NET_RSSI_INFO) 
        {
            result->rssi = value;
        } 
        else if (oid == SNMP_OID_GET_DEV_ICCID_INFO) 
        {
            result->iccid = value;
        } 
        else if (oid == SNMP_OID_GET_IMSI_INFO) 
        {
            result->imsi = value;
        } 
        else if (oid == SNMP_OID_GET_NET_TAC_INFO) 
        {
            result->tac = value;
        } 
        else if (oid == SNMP_OID_GET_NET_TYPE_INFO) 
        {
            result->net_type = value;
        }
    }

return result;
}

std::pair<std::string, std::string> kusaauto::XC_Detector::split(const std::string& str, char delimiter) 
{
    size_t pos = str.find(delimiter);
    if (pos == std::string::npos) 
    {
        return {str, ""};
    }
    return {str.substr(0, pos), str.substr(pos + 1)};
}

int kusaauto::XC_Detector::init() 
{
    int status;
    m_pSnmp = new Snmp_pp::Snmp(status);
    if (status != SNMP_CLASS_SUCCESS) 
    {
        std::cout << "Error creating SNMP session: " << m_pSnmp->error_msg(status) << std::endl;
        delete m_pSnmp;
        m_pSnmp = nullptr;
        return 1;
    }
    m_pV3MP = new Snmp_pp::v3MP("initial", 0, status);
    if (status != SNMPv3_MP_OK) 
    {
        std::cout << "Failed to create v3MP: " << m_pSnmp->error_msg(status) << std::endl;

        delete m_pSnmp;
        m_pSnmp = nullptr;
        delete m_pV3MP;
        m_pV3MP = nullptr;
        return 2; 
    }
    Snmp_pp::USM* usm = m_pV3MP->get_usm();
    if (usm == nullptr) 
    {
        std::cout << "Failed to get USM from v3MP.";

        delete m_pSnmp;
        m_pSnmp = nullptr;
        delete m_pV3MP;
        m_pV3MP = nullptr;
        return 3;
    }

    status = usm->add_usm_user(m_user.c_str(), SNMP_AUTHPROTOCOL_HMACMD5, SNMP_PRIVPROTOCOL_DES, m_password.c_str(),
                               "rootelink");
    if (status != SNMPv3_USM_OK) 
    {

        std::cout << "Failed to add USM user: " << m_pSnmp->error_msg(status) << std::endl;

        delete m_pSnmp;
        m_pSnmp = nullptr;
        delete m_pV3MP;
        m_pV3MP = nullptr;
        return 4; 
    }

    m_pSnmp->set_mpv3(m_pV3MP);
    return 0;
}
