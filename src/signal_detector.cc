#include "signal_detector.h"
#include "common/ulog.h"
#include "YR_Detector.h"
#include "XC_Detector.h"

bool SignalDetector::init() 
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (initialized_) 
    {
        return true;
    }

    try 
    {
        yr_detector_ = std::make_unique<kusaauto::YR_Detector>("192.168.1.1", "user", "authpass", true, 2000);
        xc_detector_ = std::make_unique<kusaauto::XC_Detector>("192.168.1.1", "elink", "rootelink", true, 2000);
        
        initialized_ = true;
        UINFO << "Signal detector initialized successfully";
        return true;
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Exception during signal detector initialization: " << e.what();
        return false;
    }
}

std::pair<int, int> SignalDetector::getSignalStrengths() 
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (!initialized_) 
    {
        UERROR << "Signal detector not initialized";
        return {0, 0};
    }

    try 
    {
        int youren = detectYouRenSignal();
        int xc = detectXCSignal();
        return {youren, xc};
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error getting signal strengths: " << e.what();
        return {0, 0};
    }
}

int SignalDetector::detectYouRenSignal() 
{
    try 
    {
        auto result = yr_detector_->detectYouRenSignal();
        if (!result) 
        {
            UERROR << "Failed to detect YouRen signal";
            return 0;
        }
        int dbm = std::stoi(result->rssi);
        return convertDbmToPercent(dbm);
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error detecting YouRen signal: " << e.what();
        return 0;
    }
}

int SignalDetector::detectXCSignal() 
{
    try 
    {
        auto result = xc_detector_->detectXCSiganl();
        if (!result) 
        {
            UERROR << "Failed to detect XC signal";
            return 0;
        }
        int dbm = std::stoi(result->rssi);
        return convertDbmToPercent(dbm);
    } 
    catch (const std::exception& e) 
    {
        UERROR << "Error detecting XC signal: " << e.what();
        return 0;
    }
}

int SignalDetector::convertDbmToPercent(int dbm) 
{
    int percent = (dbm + 113) * 100 / 62;
    return std::max(0, std::min(100, percent));
} 