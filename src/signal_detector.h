
#include <memory>
#include <mutex>
#include <string>
#include <utility>
#include "YR_Detector.h"
#include "XC_Detector.h"


struct SignalStrength 
{
    int strength;  
};

class SignalDetector {
public:
    static SignalDetector& getInstance() 
    {
        static SignalDetector instance;
        return instance;
    }

    bool init();

    std::pair<int, int> getSignalStrengths();

    SignalDetector(const SignalDetector&) = delete;
    SignalDetector& operator=(const SignalDetector&) = delete;
    SignalDetector(SignalDetector&&) = delete;
    SignalDetector& operator=(SignalDetector&&) = delete;

private:
    SignalDetector() = default;
    ~SignalDetector() = default;

    int detectYouRenSignal();
    int detectXCSignal();
    int convertDbmToPercent(int dbm);

    std::mutex mutex_;
    bool initialized_ = false;
    std::unique_ptr<kusaauto::YR_Detector> yr_detector_;
    std::unique_ptr<kusaauto::XC_Detector> xc_detector_;
};
