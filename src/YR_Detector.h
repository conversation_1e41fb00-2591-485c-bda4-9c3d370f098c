#ifndef YR_DETECTOR_H
#define YR_DETECTOR_H
#include <snmp_pp/libsnmp.h>
#include <snmp_pp/snmp_pp.h>
#include <snmp_pp/snmperrs.h>
#include <memory>
#include <vector>

namespace kusaauto {
    class YR_Detector {
    public:
        struct Result {
            std::string cpu;
            std::string imei;
            std::string version;
            std::string iccid;
            std::string net_type;
            std::string imsi;
            std::string rssi;
            std::string tac;

            std::string printString();
        };

        using ResultPtr = std::unique_ptr<Result>;
        YR_Detector(
            const std::string& host,
            const std::string& user,
            const std::string& password,
            bool simpleMode = false,
            int timeout = 2000);
        virtual ~YR_Detector();

        ResultPtr detectYouRenSignal();

    protected:
        int init();
        static std::pair<std::string, std::string> split(const std::string& str, char delimiter);

    private:
        std::string m_host;
        std::string m_user;
        std::string m_password;
        bool m_simpleMode;
        int m_timeout;
        Snmp_pp::Snmp* m_pSnmp;
        Snmp_pp::v3MP* m_pV3MP;
    };
}
#endif // YR_DETECTOR_H

