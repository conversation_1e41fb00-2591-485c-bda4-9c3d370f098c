#!/bin/bash

# Test script to verify resource cleanup fixes
# This script will build and test the application with proper signal handling

set -e

echo "=== Testing VTX Serial Communication Resource Cleanup ==="

# Build the application
echo "Building application..."
if [ -f "CMakeLists.txt" ]; then
    mkdir -p build
    cd build
    cmake ..
    make -j$(nproc)
    cd ..
elif [ -f "Makefile" ]; then
    make clean
    make -j$(nproc)
else
    echo "No build system found. Please build manually."
    exit 1
fi

echo "Build completed successfully."

# Check if the executable exists
APP_EXECUTABLE=""
if [ -f "build/vtx_serial_comm_app" ]; then
    APP_EXECUTABLE="build/vtx_serial_comm_app"
elif [ -f "vtx_serial_comm_app" ]; then
    APP_EXECUTABLE="vtx_serial_comm_app"
else
    echo "Executable not found. Please check build output."
    exit 1
fi

echo "Found executable: $APP_EXECUTABLE"

# Create a test configuration if it doesn't exist
if [ ! -f "test_config.json" ]; then
    cat > test_config.json << EOF
{
    "vtx_serial_comm": {
        "config_file": "conf/vtx_serial_comm/vtx_serial_comm_app.pb.txt",
        "gflag_file": "conf/vtx_serial_comm/vtx_serial_comm.gflags"
    },
    "log": {
        "level": "INFO"
    }
}
EOF
    echo "Created test configuration file."
fi

# Test 1: Normal startup and shutdown
echo ""
echo "=== Test 1: Normal Startup and Shutdown ==="
echo "Starting application for 5 seconds, then sending SIGINT..."

timeout 10s $APP_EXECUTABLE -conf test_config.json -domain 80 &
APP_PID=$!

sleep 5
echo "Sending SIGINT to process $APP_PID..."
kill -INT $APP_PID

# Wait for process to exit
wait $APP_PID 2>/dev/null || true

echo "Process exited. Checking for core dumps..."
if ls core* 1> /dev/null 2>&1; then
    echo "WARNING: Core dump found!"
    ls -la core*
else
    echo "No core dumps found. Good!"
fi

# Test 2: Quick shutdown test
echo ""
echo "=== Test 2: Quick Shutdown Test ==="
echo "Starting and immediately stopping application..."

$APP_EXECUTABLE -conf test_config.json -domain 80 &
APP_PID=$!

sleep 1
kill -INT $APP_PID
wait $APP_PID 2>/dev/null || true

if ls core* 1> /dev/null 2>&1; then
    echo "WARNING: Core dump found in quick shutdown test!"
else
    echo "Quick shutdown test passed!"
fi

# Test 3: Multiple rapid starts/stops
echo ""
echo "=== Test 3: Multiple Rapid Starts/Stops ==="
for i in {1..3}; do
    echo "Test run $i/3..."
    $APP_EXECUTABLE -conf test_config.json -domain 80 &
    APP_PID=$!
    sleep 2
    kill -INT $APP_PID
    wait $APP_PID 2>/dev/null || true
    sleep 1
done

if ls core* 1> /dev/null 2>&1; then
    echo "WARNING: Core dump found in rapid start/stop test!"
else
    echo "Rapid start/stop test passed!"
fi

echo ""
echo "=== Resource Cleanup Test Summary ==="
if ls core* 1> /dev/null 2>&1; then
    echo "❌ FAILED: Core dumps were generated during testing"
    echo "Core dumps found:"
    ls -la core*
    exit 1
else
    echo "✅ PASSED: No core dumps generated"
    echo "✅ Resource cleanup appears to be working correctly"
fi

echo ""
echo "=== Additional Checks ==="
echo "Checking for memory leaks with valgrind (if available)..."
if command -v valgrind &> /dev/null; then
    echo "Running valgrind test..."
    timeout 10s valgrind --leak-check=full --show-leak-kinds=all --track-origins=yes \
        $APP_EXECUTABLE -conf test_config.json -domain 80 2>&1 | tee valgrind_output.log &
    VALGRIND_PID=$!
    
    sleep 5
    # Find the actual application process
    APP_PID=$(pgrep -f "$APP_EXECUTABLE")
    if [ ! -z "$APP_PID" ]; then
        kill -INT $APP_PID
    fi
    
    wait $VALGRIND_PID 2>/dev/null || true
    
    if grep -q "definitely lost: 0 bytes" valgrind_output.log; then
        echo "✅ No definite memory leaks detected"
    else
        echo "⚠️  Potential memory leaks detected. Check valgrind_output.log"
    fi
else
    echo "Valgrind not available, skipping memory leak check"
fi

echo ""
echo "Testing completed!"
