#!/bin/bash
set -u
set -e

R="\033[31m";    #红色
G="\033[32m";    #绿色
Y="\033[33m";    #黄色
B="\033[34m";    #蓝色
P="\033[35m";    #紫色
BG="\033[36m";   #蓝绿色
W="\033[37m";    #白色
E="\033[0m";     #结束

BGW="\033[46;37m"; #绿底白字
YW="\033[43;37m"; #黄底白字
RW="\033[1;41;37m"; #红底白字
GW="\033[42;37m"; #绿底白字

WORKSPACE_DIR=$(dirname $(readlink -f "$0"))
INSTALL_DIR=$(cd "$WORKSPACE_DIR/../../../opt/"; pwd)
BUILD_DIR="${WORKSPACE_DIR}/build"

function clear(){  
  set +e     
  rm -rf build
#   rm -rf uttest
#   rm -rf CoverageReport
#   rm InitialCoverage.info
#   rm CoverageResult.info
#   rm Coverage.info
  set -e
  return $?
}

function build(){  
  set +e
  mkdir build
  set -e
  cd build 
  cmake -DCMAKE_INSTALL_PREFIX=$INSTALL_DIR ..
  make -j$(nproc) install  || {   
    echo "make failed"
    exit 1   
    }
  echo "make successfully!"
  return $?
}

function print_usage_and_exit() {
  echo -e  " "
  echo -e  "      $RW Usage: bash $0 [options] $E"
  echo -e  "$Y      clear:                     rm old build & uttest & coverage $E"  
  echo -e  "$Y      build:                     only build without clear $E"
  echo -e  "$Y      build_new:                 only build & clear $E"
#   echo -e  "$Y      build_and_tools:           only build & test  without clear $E"
#   echo -e  "$Y      build_all:                 build & unit test & clear $E"
  echo -e  " "
  exit -1
}

main() {
  echo -e "[32m start build.. [0m"
  echo -e "[32m workspace dir is: ${WORKSPACE_DIR} [0m"
  echo -e "[32m install dir is: ${INSTALL_DIR} [0m"
  cd $WORKSPACE_DIR

  if [ $# -eq 0 ]; then
  print_usage_and_exit
  else
    case $1 in
      build)
      build
      ;;
      build_new)
      clear
      build
      ;;
      clear)
      clear
      ;;      
      *)
      print_usage_and_exit
      ;;
    esac
  fi
  echo "everything is done, enjoy"
}

main $@  

