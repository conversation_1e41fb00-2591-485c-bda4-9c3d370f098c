cmake_minimum_required(VERSION 2.8.12)

if(NOT CMAKE_VERSION VERSION_LESS 3.0)
    cmake_policy(SET CMP0048 NEW)
endif()

project(vtx_serial_comm)

# Check C++11
include(CheckCXXCompilerFlag)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_COMPILER_IS_CLANG OR
    CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    check_cxx_compiler_flag(-std=c++11 SUPPORTS_CXX11)

    if(NOT SUPPORTS_CXX11)
        message(FATAL_ERROR "Compiler doesn't support C++11")
    endif()
endif()

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++14 -g -pthread -O2 -fPIE -fPIC ") # -g -ggdb")

message(STATUS "Configuring project...")

# 设定头文件路径
include_directories(
    ${CMAKE_SOURCE_DIR}/include
    ${CMAKE_SOURCE_DIR}/src
)

# 使用正则表达式匹配所有ARM变体（aarch64/armv7l/arm64等）
if (CMAKE_SYSTEM_PROCESSOR MATCHES "aarch64|arm.*|ARM.*")
    set(SNMP_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/arm)
    message(STATUS "Building for ARM architecture. Using SNMP library from: ${SNMP_LIB_PATH}")
else()
    set(SNMP_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/x86)
    message(STATUS "Building for x86 architecture. Using SNMP library from: ${SNMP_LIB_PATH}")
endif()


# message("include_directories" ,${include_directories})
file(GLOB_RECURSE MODULE_SRCS "src/*.cc" "src/*.cxx" "src/*.cpp")

if(NOT fastrtps_FOUND)
    find_package(fastrtps REQUIRED)
endif()

find_package(Boost 1.71 REQUIRED COMPONENTS log_setup log)

# thirdparty
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/ThirdParty.cmake)
# idl
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/AdsIdl.cmake)
# AdsComm
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/AdsTransport.cmake)
# common
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/AdsCommon.cmake)
# AdsProto
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/AdsProto.cmake)

# GFlags
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/GFlags.cmake)
# GLog
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/GLog.cmake)

# Protobuf
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/Protobuf_sys.cmake)
# Yaml
include(${CMAKE_SOURCE_DIR}/../../cmake_modules/Yaml.cmake)

add_executable(${PROJECT_NAME} ${MODULE_SRCS})

target_compile_definitions(${PROJECT_NAME} PRIVATE
    $<$<AND:$<NOT:$<BOOL:${WIN32}>>,$<STREQUAL:"${CMAKE_BUILD_TYPE}","Debug">>:__DEBUG>
    $<$<BOOL:${INTERNAL_DEBUG}>:__INTERNALDEBUG> # Internal debug activated.
)

# 找到snmp++库
find_library(SNMP++_LIBRARY NAMES libsnmp++.so.36.0.2 libsnmp++.so.36 libsnmp++.so PATHS ${SNMP_LIB_PATH} NO_DEFAULT_PATH)
message(STATUS ">> SNMP++_LIBRARY: ${SNMP++_LIBRARY}")
if(NOT SNMP++_LIBRARY)
    message(FATAL_ERROR "Could not find snmp++ library")
endif()

target_link_libraries(${PROJECT_NAME}
    ${ADS_IDL_LIBRARIES}
    ${ADS_COMMON_LIBRARIES}
    ${ADS_TRANSPORT_LIBRARIES}
    ${ADS_PROTO_LIBRARIES}
    fastrtps
    ${Boost_LIBRARIES}
    ${PROTOBUF_LIBRARIES}
    gflags
    glog
    ${Yaml_LIBRARIES}
    ${SNMP++_LIBRARY}
)

install(
    TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION ${CMAKE_INSTALL_PREFIX}/bin)

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/conf/
    DESTINATION ${CMAKE_INSTALL_PREFIX}/conf)

install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/launch/
    DESTINATION ${CMAKE_INSTALL_PREFIX}/launch)

# 安装库文件到/opt/lib（根据平台选择）
file(GLOB SNMP_LIB_FILES 
"${SNMP_LIB_PATH}/libsnmp++_static.a"
"${SNMP_LIB_PATH}/libsnmp++.so*"  # 匹配所有.so文件及符号链接
)
install(FILES ${SNMP_LIB_FILES} DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)

# install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/lib/
#     DESTINATION ${CMAKE_INSTALL_PREFIX}/lib)

