# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named vtx_serial_comm

# Build rule for target.
vtx_serial_comm: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 vtx_serial_comm
.PHONY : vtx_serial_comm

# fast build rule for target.
vtx_serial_comm/fast:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/build
.PHONY : vtx_serial_comm/fast

src/XC_Detxctor.o: src/XC_Detxctor.cpp.o

.PHONY : src/XC_Detxctor.o

# target to build an object file
src/XC_Detxctor.cpp.o:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o
.PHONY : src/XC_Detxctor.cpp.o

src/XC_Detxctor.i: src/XC_Detxctor.cpp.i

.PHONY : src/XC_Detxctor.i

# target to preprocess a source file
src/XC_Detxctor.cpp.i:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.i
.PHONY : src/XC_Detxctor.cpp.i

src/XC_Detxctor.s: src/XC_Detxctor.cpp.s

.PHONY : src/XC_Detxctor.s

# target to generate assembly for a file
src/XC_Detxctor.cpp.s:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.s
.PHONY : src/XC_Detxctor.cpp.s

src/YR_Detector.o: src/YR_Detector.cpp.o

.PHONY : src/YR_Detector.o

# target to build an object file
src/YR_Detector.cpp.o:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o
.PHONY : src/YR_Detector.cpp.o

src/YR_Detector.i: src/YR_Detector.cpp.i

.PHONY : src/YR_Detector.i

# target to preprocess a source file
src/YR_Detector.cpp.i:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.i
.PHONY : src/YR_Detector.cpp.i

src/YR_Detector.s: src/YR_Detector.cpp.s

.PHONY : src/YR_Detector.s

# target to generate assembly for a file
src/YR_Detector.cpp.s:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.s
.PHONY : src/YR_Detector.cpp.s

src/function_vtx_serial_comm.o: src/function_vtx_serial_comm.cc.o

.PHONY : src/function_vtx_serial_comm.o

# target to build an object file
src/function_vtx_serial_comm.cc.o:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o
.PHONY : src/function_vtx_serial_comm.cc.o

src/function_vtx_serial_comm.i: src/function_vtx_serial_comm.cc.i

.PHONY : src/function_vtx_serial_comm.i

# target to preprocess a source file
src/function_vtx_serial_comm.cc.i:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.i
.PHONY : src/function_vtx_serial_comm.cc.i

src/function_vtx_serial_comm.s: src/function_vtx_serial_comm.cc.s

.PHONY : src/function_vtx_serial_comm.s

# target to generate assembly for a file
src/function_vtx_serial_comm.cc.s:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.s
.PHONY : src/function_vtx_serial_comm.cc.s

src/signal_detector.o: src/signal_detector.cc.o

.PHONY : src/signal_detector.o

# target to build an object file
src/signal_detector.cc.o:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o
.PHONY : src/signal_detector.cc.o

src/signal_detector.i: src/signal_detector.cc.i

.PHONY : src/signal_detector.i

# target to preprocess a source file
src/signal_detector.cc.i:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.i
.PHONY : src/signal_detector.cc.i

src/signal_detector.s: src/signal_detector.cc.s

.PHONY : src/signal_detector.s

# target to generate assembly for a file
src/signal_detector.cc.s:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.s
.PHONY : src/signal_detector.cc.s

src/vtx_serial_comm_app_main.o: src/vtx_serial_comm_app_main.cc.o

.PHONY : src/vtx_serial_comm_app_main.o

# target to build an object file
src/vtx_serial_comm_app_main.cc.o:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o
.PHONY : src/vtx_serial_comm_app_main.cc.o

src/vtx_serial_comm_app_main.i: src/vtx_serial_comm_app_main.cc.i

.PHONY : src/vtx_serial_comm_app_main.i

# target to preprocess a source file
src/vtx_serial_comm_app_main.cc.i:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.i
.PHONY : src/vtx_serial_comm_app_main.cc.i

src/vtx_serial_comm_app_main.s: src/vtx_serial_comm_app_main.cc.s

.PHONY : src/vtx_serial_comm_app_main.s

# target to generate assembly for a file
src/vtx_serial_comm_app_main.cc.s:
	$(MAKE) -f CMakeFiles/vtx_serial_comm.dir/build.make CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.s
.PHONY : src/vtx_serial_comm_app_main.cc.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... install/strip"
	@echo "... install/local"
	@echo "... install"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... edit_cache"
	@echo "... vtx_serial_comm"
	@echo "... src/XC_Detxctor.o"
	@echo "... src/XC_Detxctor.i"
	@echo "... src/XC_Detxctor.s"
	@echo "... src/YR_Detector.o"
	@echo "... src/YR_Detector.i"
	@echo "... src/YR_Detector.s"
	@echo "... src/function_vtx_serial_comm.o"
	@echo "... src/function_vtx_serial_comm.i"
	@echo "... src/function_vtx_serial_comm.s"
	@echo "... src/signal_detector.o"
	@echo "... src/signal_detector.i"
	@echo "... src/signal_detector.s"
	@echo "... src/vtx_serial_comm_app_main.o"
	@echo "... src/vtx_serial_comm_app_main.i"
	@echo "... src/vtx_serial_comm_app_main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

