# Install script for directory: /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/mnt/data/workspace/auto_pro/opt")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm")
    file(RPATH_CHECK
         FILE "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm"
         RPATH "")
  endif()
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/mnt/data/workspace/auto_pro/opt/bin" TYPE EXECUTABLE FILES "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/vtx_serial_comm")
  if(EXISTS "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm" AND
     NOT IS_SYMLINK "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm")
    file(RPATH_CHANGE
         FILE "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm"
         OLD_RPATH "/mnt/data/workspace/auto_pro/modules/cmake_modules/../../third_party/aarch64/lib:/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/arm:/mnt/data/workspace/auto_pro/opt/lib:"
         NEW_RPATH "")
    if(CMAKE_INSTALL_DO_STRIP)
      execute_process(COMMAND "/usr/bin/strip" "$ENV{DESTDIR}/mnt/data/workspace/auto_pro/opt/bin/vtx_serial_comm")
    endif()
  endif()
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mnt/data/workspace/auto_pro/opt/conf/")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/mnt/data/workspace/auto_pro/opt/conf" TYPE DIRECTORY FILES "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/conf/")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mnt/data/workspace/auto_pro/opt/launch/")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/mnt/data/workspace/auto_pro/opt/launch" TYPE DIRECTORY FILES "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/launch/")
endif()

if("x${CMAKE_INSTALL_COMPONENT}x" STREQUAL "xUnspecifiedx" OR NOT CMAKE_INSTALL_COMPONENT)
  list(APPEND CMAKE_ABSOLUTE_DESTINATION_FILES
   "/mnt/data/workspace/auto_pro/opt/lib/libsnmp++.so;/mnt/data/workspace/auto_pro/opt/lib/libsnmp++.so.36;/mnt/data/workspace/auto_pro/opt/lib/libsnmp++.so.36.0.2;/mnt/data/workspace/auto_pro/opt/lib/libsnmp++_static.a")
  if(CMAKE_WARN_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(WARNING "ABSOLUTE path INSTALL DESTINATION : ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
  if(CMAKE_ERROR_ON_ABSOLUTE_INSTALL_DESTINATION)
    message(FATAL_ERROR "ABSOLUTE path INSTALL DESTINATION forbidden (by caller): ${CMAKE_ABSOLUTE_DESTINATION_FILES}")
  endif()
file(INSTALL DESTINATION "/mnt/data/workspace/auto_pro/opt/lib" TYPE FILE FILES
    "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/arm/libsnmp++.so"
    "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/arm/libsnmp++.so.36"
    "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/arm/libsnmp++.so.36.0.2"
    "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/arm/libsnmp++_static.a"
    )
endif()

if(CMAKE_INSTALL_COMPONENT)
  set(CMAKE_INSTALL_MANIFEST "install_manifest_${CMAKE_INSTALL_COMPONENT}.txt")
else()
  set(CMAKE_INSTALL_MANIFEST "install_manifest.txt")
endif()

string(REPLACE ";" "\n" CMAKE_INSTALL_MANIFEST_CONTENT
       "${CMAKE_INSTALL_MANIFEST_FILES}")
file(WRITE "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/${CMAKE_INSTALL_MANIFEST}"
     "${CMAKE_INSTALL_MANIFEST_CONTENT}")
