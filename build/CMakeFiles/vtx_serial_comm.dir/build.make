# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build

# Include any dependencies generated for this target.
include CMakeFiles/vtx_serial_comm.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/vtx_serial_comm.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/vtx_serial_comm.dir/flags.make

CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o: CMakeFiles/vtx_serial_comm.dir/flags.make
CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o: ../src/XC_Detxctor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o -c /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/XC_Detxctor.cpp

CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/XC_Detxctor.cpp > CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.i

CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/XC_Detxctor.cpp -o CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.s

CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o: CMakeFiles/vtx_serial_comm.dir/flags.make
CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o: ../src/YR_Detector.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o -c /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/YR_Detector.cpp

CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/YR_Detector.cpp > CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.i

CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/YR_Detector.cpp -o CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.s

CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o: CMakeFiles/vtx_serial_comm.dir/flags.make
CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o: ../src/function_vtx_serial_comm.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o -c /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/function_vtx_serial_comm.cc

CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/function_vtx_serial_comm.cc > CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.i

CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/function_vtx_serial_comm.cc -o CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.s

CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o: CMakeFiles/vtx_serial_comm.dir/flags.make
CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o: ../src/signal_detector.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o -c /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/signal_detector.cc

CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/signal_detector.cc > CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.i

CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/signal_detector.cc -o CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.s

CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o: CMakeFiles/vtx_serial_comm.dir/flags.make
CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o: ../src/vtx_serial_comm_app_main.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o"
	/usr/bin/c++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o -c /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/vtx_serial_comm_app_main.cc

CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/vtx_serial_comm_app_main.cc > CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.i

CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/vtx_serial_comm_app_main.cc -o CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.s

# Object files for target vtx_serial_comm
vtx_serial_comm_OBJECTS = \
"CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o" \
"CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o" \
"CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o" \
"CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o" \
"CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o"

# External object files for target vtx_serial_comm
vtx_serial_comm_EXTERNAL_OBJECTS =

vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o
vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o
vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o
vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o
vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o
vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/build.make
vtx_serial_comm: /mnt/data/workspace/auto_pro/opt/lib/libads_idl.so
vtx_serial_comm: /mnt/data/workspace/auto_pro/opt/lib/libads_common.so
vtx_serial_comm: /mnt/data/workspace/auto_pro/opt/lib/libads_transport.so
vtx_serial_comm: /mnt/data/workspace/auto_pro/opt/lib/libads_proto.so
vtx_serial_comm: /usr/lib/libfastrtps.so.2.14.4
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_log_setup.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_log.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libprotobuf-lite.so
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libprotobuf.so
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libgflags.so.2.2.2
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libyaml-cpp.so
vtx_serial_comm: ../lib/arm/libsnmp++.so.36.0.2
vtx_serial_comm: /usr/lib/libfastcdr.so.2.2.5
vtx_serial_comm: /usr/lib/libfoonathan_memory-0.7.3.so
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libtinyxml2.so
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libssl.so
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libcrypto.so
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_regex.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0
vtx_serial_comm: /usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.71.0
vtx_serial_comm: CMakeFiles/vtx_serial_comm.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking CXX executable vtx_serial_comm"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/vtx_serial_comm.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/vtx_serial_comm.dir/build: vtx_serial_comm

.PHONY : CMakeFiles/vtx_serial_comm.dir/build

CMakeFiles/vtx_serial_comm.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/vtx_serial_comm.dir/cmake_clean.cmake
.PHONY : CMakeFiles/vtx_serial_comm.dir/clean

CMakeFiles/vtx_serial_comm.dir/depend:
	cd /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/vtx_serial_comm.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/vtx_serial_comm.dir/depend

