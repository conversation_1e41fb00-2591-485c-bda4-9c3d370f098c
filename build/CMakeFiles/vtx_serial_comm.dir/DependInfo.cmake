# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/XC_Detxctor.cpp" "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o"
  "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/YR_Detector.cpp" "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o"
  "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/function_vtx_serial_comm.cc" "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o"
  "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/signal_detector.cc" "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o"
  "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/src/vtx_serial_comm_app_main.cc" "/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "BOOST_ALL_NO_LIB"
  "BOOST_ATOMIC_DYN_LINK"
  "BOOST_CHRONO_DYN_LINK"
  "BOOST_DATE_TIME_DYN_LINK"
  "BOOST_FILESYSTEM_DYN_LINK"
  "BOOST_LOG_DYN_LINK"
  "BOOST_LOG_SETUP_DYN_LINK"
  "BOOST_REGEX_DYN_LINK"
  "BOOST_THREAD_DYN_LINK"
  "FASTCDR_DYN_LINK"
  "FASTRTPS_DYN_LINK"
  "FOONATHAN_MEMORY=1"
  "FOONATHAN_MEMORY_VERSION_MAJOR=0"
  "FOONATHAN_MEMORY_VERSION_MINOR=7"
  "FOONATHAN_MEMORY_VERSION_PATCH=3"
  "GFLAGS_IS_A_DLL=0"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../include"
  "../src"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/../../third_party/aarch64/include"
  "/mnt/data/workspace/auto_pro/opt/include"
  "/mnt/data/workspace/auto_pro/opt/include/openads/modules/ads_transport"
  "/mnt/data/workspace/auto_pro/opt/include/openads/modules/ads_common"
  "/usr/include/foonathan_memory"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
