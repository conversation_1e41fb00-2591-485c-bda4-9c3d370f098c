/usr/bin/c++   -std=c++14 -g -pthread -O2 -fPIE -fPIC   -rdynamic CMakeFiles/vtx_serial_comm.dir/src/XC_Detxctor.cpp.o CMakeFiles/vtx_serial_comm.dir/src/YR_Detector.cpp.o CMakeFiles/vtx_serial_comm.dir/src/function_vtx_serial_comm.cc.o CMakeFiles/vtx_serial_comm.dir/src/signal_detector.cc.o CMakeFiles/vtx_serial_comm.dir/src/vtx_serial_comm_app_main.cc.o  -o vtx_serial_comm   -L/mnt/data/workspace/auto_pro/modules/cmake_modules/../../third_party/aarch64/lib  -L/mnt/data/workspace/auto_pro/opt/lib  -Wl,-rpath,/mnt/data/workspace/auto_pro/modules/cmake_modules/../../third_party/aarch64/lib:/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/arm:/mnt/data/workspace/auto_pro/opt/lib: /mnt/data/workspace/auto_pro/opt/lib/libads_idl.so /mnt/data/workspace/auto_pro/opt/lib/libads_common.so /mnt/data/workspace/auto_pro/opt/lib/libads_transport.so /mnt/data/workspace/auto_pro/opt/lib/libads_proto.so /usr/lib/libfastrtps.so.2.14.4 /usr/lib/aarch64-linux-gnu/libboost_log_setup.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_log.so.1.71.0 -lprotobuf-lite -lprotobuf /usr/lib/aarch64-linux-gnu/libgflags.so.2.2.2 -lglog -lyaml-cpp ../lib/arm/libsnmp++.so.36.0.2 /usr/lib/libfastcdr.so.2.2.5 /usr/lib/libfoonathan_memory-0.7.3.so -ldl -ltinyxml2 /usr/lib/aarch64-linux-gnu/libssl.so /usr/lib/aarch64-linux-gnu/libcrypto.so -lrt /usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_regex.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.71.0 -lpthread 
