# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "../CMakeLists.txt"
  "CMakeFiles/3.16.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeSystem.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/AdsCommon.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/AdsIdl.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/AdsProto.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/AdsTransport.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/GFlags.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/GLog.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/Protobuf_sys.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/ThirdParty.cmake"
  "/mnt/data/workspace/auto_pro/modules/cmake_modules/Yaml.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/Boost-1.71.0/BoostConfig.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/Boost-1.71.0/BoostConfigVersion.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/BoostDetectToolset-1.71.0.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_atomic-1.71.0/boost_atomic-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_atomic-1.71.0/boost_atomic-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_atomic-1.71.0/libboost_atomic-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_atomic-1.71.0/libboost_atomic-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_chrono-1.71.0/boost_chrono-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_chrono-1.71.0/boost_chrono-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_chrono-1.71.0/libboost_chrono-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_chrono-1.71.0/libboost_chrono-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_date_time-1.71.0/boost_date_time-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_date_time-1.71.0/boost_date_time-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_date_time-1.71.0/libboost_date_time-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_date_time-1.71.0/libboost_date_time-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_filesystem-1.71.0/boost_filesystem-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_filesystem-1.71.0/boost_filesystem-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_filesystem-1.71.0/libboost_filesystem-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_filesystem-1.71.0/libboost_filesystem-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_headers-1.71.0/boost_headers-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_headers-1.71.0/boost_headers-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log-1.71.0/boost_log-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log-1.71.0/boost_log-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log-1.71.0/libboost_log-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log-1.71.0/libboost_log-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log_setup-1.71.0/boost_log_setup-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log_setup-1.71.0/boost_log_setup-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log_setup-1.71.0/libboost_log_setup-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_log_setup-1.71.0/libboost_log_setup-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_regex-1.71.0/boost_regex-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_regex-1.71.0/boost_regex-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_regex-1.71.0/libboost_regex-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_regex-1.71.0/libboost_regex-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.71.0/boost_thread-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.71.0/boost_thread-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.71.0/libboost_thread-variant-shared.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.71.0/libboost_thread-variant-static.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/gflags/gflags-config-version.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/gflags/gflags-config.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/gflags/gflags-nonamespace-targets-release.cmake"
  "/usr/lib/aarch64-linux-gnu/cmake/gflags/gflags-nonamespace-targets.cmake"
  "/usr/lib/cmake/fastcdr/fastcdr-config-version.cmake"
  "/usr/lib/cmake/fastcdr/fastcdr-config.cmake"
  "/usr/lib/cmake/fastcdr/fastcdr-shared-targets-release.cmake"
  "/usr/lib/cmake/fastcdr/fastcdr-shared-targets.cmake"
  "/usr/lib/foonathan_memory/cmake/foonathan_memory-config-noconfig.cmake"
  "/usr/lib/foonathan_memory/cmake/foonathan_memory-config-version.cmake"
  "/usr/lib/foonathan_memory/cmake/foonathan_memory-config.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCCompiler.cmake.in"
  "/usr/share/cmake-3.16/Modules/CMakeCCompilerABI.c"
  "/usr/share/cmake-3.16/Modules/CMakeCInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCXXCompiler.cmake.in"
  "/usr/share/cmake-3.16/Modules/CMakeCXXCompilerABI.cpp"
  "/usr/share/cmake-3.16/Modules/CMakeCXXInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeCompilerIdDetection.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineCCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineCXXCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineCompileFeatures.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineCompilerABI.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineCompilerId.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeDetermineSystem.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeFindBinUtils.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeFindDependencyMacro.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeGenericSystem.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeInitializeConfigs.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeLanguageInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeParseImplicitLinkInfo.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeSystem.cmake.in"
  "/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeTestCCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeTestCXXCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeTestCompilerCommon.cmake"
  "/usr/share/cmake-3.16/Modules/CMakeUnixFindMake.cmake"
  "/usr/share/cmake-3.16/Modules/CheckCSourceCompiles.cmake"
  "/usr/share/cmake-3.16/Modules/CheckCXXCompilerFlag.cmake"
  "/usr/share/cmake-3.16/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/share/cmake-3.16/Modules/CheckFunctionExists.c"
  "/usr/share/cmake-3.16/Modules/CheckIncludeFile.c.in"
  "/usr/share/cmake-3.16/Modules/CheckIncludeFile.cmake"
  "/usr/share/cmake-3.16/Modules/CheckLibraryExists.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-C.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-CXX.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU-FindBinUtils.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/GNU.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/TI-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "/usr/share/cmake-3.16/Modules/FindBoost.cmake"
  "/usr/share/cmake-3.16/Modules/FindOpenSSL.cmake"
  "/usr/share/cmake-3.16/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/share/cmake-3.16/Modules/FindPackageMessage.cmake"
  "/usr/share/cmake-3.16/Modules/FindPkgConfig.cmake"
  "/usr/share/cmake-3.16/Modules/FindThreads.cmake"
  "/usr/share/cmake-3.16/Modules/Internal/CMakeCheckCompilerFlag.cmake"
  "/usr/share/cmake-3.16/Modules/Internal/FeatureTesting.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-Determine-CXX.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux-GNU.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/Linux.cmake"
  "/usr/share/cmake-3.16/Modules/Platform/UnixPaths.cmake"
  "/usr/share/fastrtps/cmake/fast-discovery-server-targets-release.cmake"
  "/usr/share/fastrtps/cmake/fast-discovery-server-targets.cmake"
  "/usr/share/fastrtps/cmake/fastrtps-config-version.cmake"
  "/usr/share/fastrtps/cmake/fastrtps-config.cmake"
  "/usr/share/fastrtps/cmake/fastrtps-shared-targets-release.cmake"
  "/usr/share/fastrtps/cmake/fastrtps-shared-targets.cmake"
  "/usr/share/fastrtps/cmake/optionparser-targets.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/3.16.3/CMakeSystem.cmake"
  "CMakeFiles/3.16.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.16.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/vtx_serial_comm.dir/DependInfo.cmake"
  )
