Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_9c2d2/fast && /usr/bin/make -f CMakeFiles/cmTC_9c2d2.dir/build.make CMakeFiles/cmTC_9c2d2.dir/build
make[1]: Entering directory '/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_9c2d2.dir/src.c.o
/usr/bin/cc   -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_9c2d2.dir/src.c.o   -c /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_9c2d2
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_9c2d2.dir/link.txt --verbose=1
/usr/bin/cc  -DCMAKE_HAVE_LIBC_PTHREAD    -rdynamic CMakeFiles/cmTC_9c2d2.dir/src.c.o  -o cmTC_9c2d2 
/usr/bin/ld: CMakeFiles/cmTC_9c2d2.dir/src.c.o: in function `main':
src.c:(.text+0x48): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x50): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5c): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_9c2d2.dir/build.make:87: cmTC_9c2d2] Error 1
make[1]: Leaving directory '/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_9c2d2/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_aedb3/fast && /usr/bin/make -f CMakeFiles/cmTC_aedb3.dir/build.make CMakeFiles/cmTC_aedb3.dir/build
make[1]: Entering directory '/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_aedb3.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_aedb3.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_aedb3
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_aedb3.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create    -rdynamic CMakeFiles/cmTC_aedb3.dir/CheckFunctionExists.c.o  -o cmTC_aedb3  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_aedb3.dir/build.make:87: cmTC_aedb3] Error 1
make[1]: Leaving directory '/mnt/data/workspace/auto_pro/modules/applications/vtx_serial_comm/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_aedb3/fast] Error 2



