domain_id: 80
qos_path: "./conf/vtx_serial_comm/vtx_serial_comm_fastdds_conf.xml"
function_configs{
    name: "VtxSerialCommFunc"
    config_path: "/user/appointed/path"
    enable_app: true
    abstract_configs{
        m_type: USER
        s_type: NONE
        config_file: "/user/appointed/conf/file"
        abstract_name: "vtx_serial_comm"
    }

    comm_configs{
        type: SUB
        topic_name: "rt/vtx/adas/cloud/v1/vehicleStatus"
        enable_local_buf_sync: false
        abstract_name: "VtxData"        
    }

    scheduler_conf{
        name: "VtxSerialCommFunc"
        tasks{
            name: "VtxSerialCommvtx_serial_commRun"
            cpuset: "1"
            policy: "SCHED_RR"
            prio: 80
        }

    }
}        
