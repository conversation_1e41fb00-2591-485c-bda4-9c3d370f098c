
<?xml version="1.0" encoding="UTF-8" ?>
<dds xmlns="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles" >
    <profiles>
        <!--TRANSPORT-->
        <transport_descriptors>
            <transport_descriptor>
                <transport_id>TransportId_TCPv4</transport_id>
                <type>TCPv4</type>
                <sendBufferSize>8912896</sendBufferSize>
                <receiveBufferSize>8912896</receiveBufferSize>
                <interfaceWhiteList>
                    <!--address>************</address-->
                    <address>*************</address>
                    <address>127.0.0.1</address>
                </interfaceWhiteList>
                <listening_ports>
                    <port>5100</port>
                    <port>5200</port>
                </listening_ports>
            </transport_descriptor>
            <transport_descriptor>
                <transport_id>TransportId_UDPv6</transport_id>
                <type>UDPv6</type>
            </transport_descriptor>
            <!-- SHM sample transport descriptor -->
            <transport_descriptor>
                <transport_id>TransportId_SHM</transport_id>
                    <type>SHM</type> <!-- REQUIRED -->
                    <maxMessageSize>2097152</maxMessageSize> <!-- OPTIONAL uint32 valid of all transports-->
                    <segment_size>2097152</segment_size> <!-- OPTIONAL uint32 SHM only-->
                    <port_queue_capacity>1024</port_queue_capacity> <!-- OPTIONAL uint32 SHM only-->
                    <healthy_check_timeout_ms>250</healthy_check_timeout_ms> <!-- OPTIONAL uint32 SHM only-->
                    <rtps_dump_file>test_file.dump</rtps_dump_file> <!-- OPTIONAL string SHM only-->
            </transport_descriptor>
        </transport_descriptors>

        <!--PARTICIPANT-->
        <participant profile_name="participant_profile">
            <domainId>80</domainId>
            <rtps>
                <name>do_avm_app</name>
                <builtin>
                    <discovery_config>
                        <leaseDuration>
                            <sec>DURATION_INFINITY</sec>
                        </leaseDuration>
                        <leaseAnnouncement>
                            <sec>1</sec>
                            <nanosec>0</nanosec>
                        </leaseAnnouncement>
                    </discovery_config>
                </builtin>
            </rtps>
        </participant>

        <!-- DATAWRITER -->
        <data_writer profile_name="datafront_binocular_left_pub_profile">
            <topic>
                <kind>NO_KEY</kind>
                <historyQos>
                    <kind>KEEP_LAST</kind>
                    <depth>10</depth>
                </historyQos>
                <resourceLimitsQos>
                    <max_samples>50</max_samples>
                    <allocated_samples>20</allocated_samples>
                </resourceLimitsQos>
            </topic>
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <publishMode>
                    <kind>ASYNCHRONOUS</kind>
                </publishMode>
            </qos>
            <times>
                <heartbeatPeriod>
                    <sec>2</sec>
                    <nanosec>200 * 1000 * 1000</nanosec>
                </heartbeatPeriod>
            </times>
        </data_writer>
        <data_writer profile_name="datafront_binocular_right_pub_profile">
            <topic>
                <kind>NO_KEY</kind>
                <historyQos>
                    <kind>KEEP_LAST</kind>
                    <depth>10</depth>
                </historyQos>
                <resourceLimitsQos>
                    <max_samples>50</max_samples>
                    <allocated_samples>20</allocated_samples>
                </resourceLimitsQos>
            </topic>
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <publishMode>
                    <kind>ASYNCHRONOUS</kind>
                </publishMode>
            </qos>
            <times>
                <heartbeatPeriod>
                    <sec>2</sec>
                    <nanosec>200 * 1000 * 1000</nanosec>
                </heartbeatPeriod>
            </times>
        </data_writer>
        <data_writer profile_name="dataobstacle_pub_profile">
            <topic>
                <kind>NO_KEY</kind>
                <historyQos>
                    <kind>KEEP_LAST</kind>
                    <depth>10</depth>
                </historyQos>
                <resourceLimitsQos>
                    <max_samples>50</max_samples>
                    <allocated_samples>20</allocated_samples>
                </resourceLimitsQos>
            </topic>
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <publishMode>
                    <kind>ASYNCHRONOUS</kind>
                </publishMode>
            </qos>
            <times>
                <heartbeatPeriod>
                    <sec>2</sec>
                    <nanosec>200 * 1000 * 1000</nanosec>
                </heartbeatPeriod>
            </times>
        </data_writer>
        <data_writer profile_name="datafreespace_pub_profile">
            <topic>
                <kind>NO_KEY</kind>
                <historyQos>
                    <kind>KEEP_LAST</kind>
                    <depth>10</depth>
                </historyQos>
                <resourceLimitsQos>
                    <max_samples>50</max_samples>
                    <allocated_samples>20</allocated_samples>
                </resourceLimitsQos>
            </topic>
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <publishMode>
                    <kind>ASYNCHRONOUS</kind>
                </publishMode>
            </qos>
            <times>
                <heartbeatPeriod>
                    <sec>2</sec>
                    <nanosec>200 * 1000 * 1000</nanosec>
                </heartbeatPeriod>
            </times>
        </data_writer>
        <data_writer profile_name="dataavp_pub_profile">
            <topic>
                <kind>NO_KEY</kind>
                <historyQos>
                    <kind>KEEP_LAST</kind>
                    <depth>10</depth>
                </historyQos>
                <resourceLimitsQos>
                    <max_samples>50</max_samples>
                    <allocated_samples>20</allocated_samples>
                </resourceLimitsQos>
            </topic>
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <publishMode>
                    <kind>ASYNCHRONOUS</kind>
                </publishMode>
            </qos>
            <times>
                <heartbeatPeriod>
                    <sec>2</sec>
                    <nanosec>200 * 1000 * 1000</nanosec>
                </heartbeatPeriod>
            </times>
        </data_writer>

        <!-- DATAREADER -->

    </profiles>
</dds>
