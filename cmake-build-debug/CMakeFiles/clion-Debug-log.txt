/opt/clion/clion-2024.1.3/bin/cmake/linux/x64/bin/cmake -DCMAKE_BUILD_TYPE=Debug -DC<PERSON>KE_MAKE_PROGRAM=/opt/clion/clion-2024.1.3/bin/ninja/linux/x64/ninja -G Ninja -S /home/<USER>/workspace/auto_pro/modules/applications/vtx_serial_comm -B /home/<USER>/workspace/auto_pro/modules/applications/vtx_serial_comm/cmake-build-debug
CMake Deprecation Warning at CMakeLists.txt:1 (cmake_minimum_required):
  Compatibility with CMake < 3.5 will be removed from a future version of
  CMake.

  Update the VERSION argument <min> value or use a ...<max> suffix to tell
  <PERSON><PERSON>ake that the project does not need compatibility with older versions.


-- The C compiler identification is GNU 11.4.0
-- The CXX compiler identification is GNU 11.4.0
-- Detecting C compiler ABI info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: /usr/bin/cc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler <PERSON><PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: /usr/bin/c++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Performing Test SUPPORTS_CXX11
-- Performing Test SUPPORTS_CXX11 - Success
-- Configuring project...
-- Building for x86 architecture. Using SNMP library from: /home/<USER>/workspace/auto_pro/modules/applications/vtx_serial_comm/lib/x86
CMake Error at CMakeLists.txt:45 (find_package):
  By not providing "Findfastrtps.cmake" in CMAKE_MODULE_PATH this project has
  asked CMake to find a package configuration file provided by "fastrtps",
  but CMake did not find one.

  Could not find a package configuration file provided by "fastrtps" with any
  of the following names:

    fastrtpsConfig.cmake
    fastrtps-config.cmake

  Add the installation prefix of "fastrtps" to CMAKE_PREFIX_PATH or set
  "fastrtps_DIR" to a directory containing one of the above files.  If
  "fastrtps" provides a separate development package or SDK, be sure it has
  been installed.


-- Configuring incomplete, errors occurred!
